import os
import json
import threading
import queue
import logging
from typing import Dict, Any, Callable, List
from tkinter import messagebox

from config import Config
from quiz_processor.document_to_quiz_processor import DocumentToQuizProcessor
from utils.helpers import scan_directory_structure

logger = logging.getLogger(__name__)

class ProcessingManager:
    def __init__(self, root, gui_vars: Dict[str, Any], status_var: tk.StringVar):
        self.root = root
        self.gui_vars = gui_vars
        self.status_var = status_var
        self.processing_thread = None
        self.question_queue = queue.Queue()
        self.is_processing = False
        self.has_generated_questions = False

    def execute(self) -> None:
        if self.is_processing:
            messagebox.showwarning("警告", "正在处理中，请等待完成或停止当前处理")
            return

        try:
            input_path = self.gui_vars['file_path_var'].get()
            if not input_path:
                messagebox.showwarning("警告", "请先选择文件或文件夹")
                return

            self.is_processing = True
            self.gui_vars['execute_btn'].config(state=tk.DISABLED, text="处理中...")
            self.gui_vars['stop_btn'].config(state=tk.NORMAL)
            self.status_var.set("准备开始处理...")

            self.processing_thread = threading.Thread(
                target=self.process_documents_thread,
                args=(input_path,),
                daemon=True
            )
            self.processing_thread.start()

        except Exception as e:
            logger.error(f"启动处理失败: {str(e)}")
            messagebox.showerror("错误", f"启动处理失败: {str(e)}")
            self.processing_complete()

    def process_documents_thread(self, input_path: str) -> None:
        try:
            config = Config()
            config.API_BASE_URL = self.gui_vars['api_base_var'].get()
            config.API_KEY = self.gui_vars['api_key_var'].get()
            config.MODEL_NAME = self.gui_vars['model_name_var'].get()
            config.TEMPERATURE = self.gui_vars['temperature_var'].get()
            config.MAX_TOKENS = self.gui_vars['max_tokens_var'].get()
            config.MAX_CHUNK_SIZE = self.gui_vars['max_chunk_size_var'].get()

            config.SINGLE_CHOICE_COUNT = self.gui_vars['single_choice_var'].get()
            config.MULTIPLE_CHOICE_COUNT = self.gui_vars['multiple_choice_var'].get()
            config.FILL_BLANK_COUNT = self.gui_vars['fill_blank_var'].get()
            config.SHORT_ANSWER_COUNT = self.gui_vars['short_answer_var'].get()
            config.TRUE_FALSE_COUNT = self.gui_vars['true_false_var'].get()
            config.SORTING_COUNT = self.gui_vars['sorting_var'].get()

            config.ENABLE_PDF = self.gui_vars['enable_pdf_var'].get()
            config.ENABLE_DOCX = self.gui_vars['enable_docx_var'].get()
            config.ENABLE_MD = self.gui_vars['enable_md_var'].get()
            config.ENABLE_TXT = self.gui_vars['enable_txt_var'].get()

            config.DISABLE_DOCUMENT_SPLITTING = self.gui_vars['disable_splitting_var'].get()
            config.ENABLE_CHUNK_MERGING = self.gui_vars['enable_chunk_merging_var'].get()
            config.USE_NEW_SPLITTING_LOGIC = self.gui_vars['use_new_splitting_var'].get()
            config.BASE_CHAR_THRESHOLD = self.gui_vars['question_base_chars_var'].get()

            config.QUESTIONS_PER_CHUNK = (
                config.SINGLE_CHOICE_COUNT +
                config.MULTIPLE_CHOICE_COUNT +
                config.FILL_BLANK_COUNT +
                config.SHORT_ANSWER_COUNT +
                config.TRUE_FALSE_COUNT +
                config.SORTING_COUNT
            )

            if self.gui_vars['csv_path_var'].get():
                config.OUTPUT_DIR = self.gui_vars['csv_path_var'].get()
            if self.gui_vars['csv_name_var'].get():
                config.CSV_FILENAME = self.gui_vars['csv_name_var'].get()

            processor = DocumentToQuizProcessor(config)

            recursive = self.gui_vars['recursive_var'].get()
            result = self.process_with_progress_callback(processor, input_path, recursive)

            if result.get('status') == 'stopped':
                self.question_queue.put({
                    'type': 'status',
                    'data': f"处理已停止。已生成题目数: {result.get('total_questions', 0)}"
                })
            elif 'error' in result:
                self.question_queue.put({
                    'type': 'error',
                    'data': result['error']
                })
            else:
                success_msg = f"处理完成！\n"
                success_msg += f"生成题目数: {result.get('total_questions', 0)}\n"
                success_msg += f"处理文件数: {result.get('processed_files_count', 0)}\n"
                success_msg += f"成功率: {result.get('success_rate', 0):.1f}%"

                if 'csv_file' in result:
                    success_msg += f"\n输出文件: {result['csv_file']}"

                self.question_queue.put({
                    'type': 'status',
                    'data': success_msg
                })
                self.question_queue.put({'type': 'complete'})

        except Exception as e:
            logger.error(f"处理线程失败: {str(e)}")
            self.question_queue.put({
                'type': 'error',
                'data': f"处理失败: {str(e)}"
            })
        finally:
            self.processing_complete()

    def process_with_progress_callback(self, processor: DocumentToQuizProcessor, input_path: str, recursive: bool) -> Dict[str, Any]:
        try:
            self.question_queue.put({
                'type': 'status',
                'data': '正在读取文档...'
            })

            documents = processor.doc_reader.batch_read_documents(input_path, recursive)
            if not documents:
                return {'error': '没有找到可处理的文档'}

            self.question_queue.put({
                'type': 'status',
                'data': '正在分割文本...'
            })
            chunks = processor.text_splitter.batch_split_documents(documents)
            if not chunks:
                return {'error': '文本分割失败'}

            self.question_queue.put({
                'type': 'status',
                'data': '开始生成题目...'
            })

            all_questions = []
            failed_chunks = []
            successful_chunk_indices = []

            total_chunks = len(chunks)
            i = 0

            while i < len(chunks):
                if not self.is_processing:
                    logger.info("用户请求停止处理。")
                    self.question_queue.put({'type': 'status', 'data': '处理已停止。'})
                    return {'status': 'stopped', 'total_questions': len(all_questions), 'processed_files_count': i}

                chunk = chunks[i]

                if chunk is None:
                    filename_for_log = '未知文件'
                    logger.warning(f"检测到空文本块，跳过处理 - 文件: {filename_for_log}, 索引: {i}")
                    self.question_queue.put({
                        'type': 'failure',
                        'data': {
                            'filename': filename_for_log,
                            'chunk_index': i,
                            'reason': '空文本块',
                            'chunk_content': 'None'
                        }
                    })
                    i += 1
                    continue

                try:
                    if chunk is None or not isinstance(chunk, dict):
                        logger.warning(f"在try块中检测到无效的chunk，跳过处理 - 索引: {i}")
                        i += 1
                        continue

                    safe_filename = chunk.get('filename', '未知文件') if chunk and isinstance(chunk, dict) else '未知文件'
                    safe_content = chunk.get('content', '') if chunk and isinstance(chunk, dict) else ''

                    self.question_queue.put({
                        'type': 'progress',
                        'data': {
                            'current': i + 1,
                            'total': total_chunks,
                            'filename': safe_filename
                        }
                    })

                    char_count = len(safe_content.strip())
                    question_counts = processor.question_calculator.calculate_question_counts(char_count)

                    quiz_data = processor.llm_client.generate_quiz(
                        content=safe_content,
                        source_filename=safe_filename,
                        question_counts=question_counts
                    )

                    if quiz_data is None:
                        if processor.config.DISABLE_DOCUMENT_SPLITTING:
                            logger.warning(f"处理文本块失败: {safe_filename} - 原因: 文档分割已禁用，内容不足以生成题目")
                            failed_chunk = {
                                **chunk,
                                'failure_reason': 'insufficient_content_no_splitting'
                            }
                            failed_chunks.append(failed_chunk)
                            self.question_queue.put({
                                'type': 'log_only_failure',
                                'data': {
                                    'filename': safe_filename,
                                    'chunk_index': chunk.get('chunk_index', 0) if chunk and isinstance(chunk, dict) else 0,
                                    'reason': '文档分割已禁用，内容不足以生成题目',
                                    'chunk_content': safe_content
                                }
                            })
                            i += 1
                        elif not processor.config.ENABLE_CHUNK_MERGING:
                            logger.warning(f"处理文本块失败: {safe_filename} - 原因: 分块合并已禁用，内容不足以生成题目")
                            failed_chunk = {
                                **chunk,
                                'failure_reason': 'insufficient_content_no_merging'
                            }
                            failed_chunks.append(failed_chunk)
                            self.question_queue.put({
                                'type': 'log_only_failure',
                                'data': {
                                    'filename': safe_filename,
                                    'chunk_index': chunk.get('chunk_index', 0) if chunk and isinstance(chunk, dict) else 0,
                                    'reason': '分块合并已禁用，内容不足以生成题目',
                                    'chunk_content': safe_content
                                }
                            })
                            i += 1
                        else:
                            merged_result = processor._try_merge_chunks_for_generation(chunks, i)
                            if merged_result['success']:
                                merged_chunk = merged_result['merged_chunk']
                                next_index = merged_result['next_index']
                                safe_merged_filename = merged_chunk.get('filename', '未知文件') if merged_chunk else '未知文件'
                                safe_merged_char_count = merged_chunk.get('char_count', 0) if merged_chunk else 0
                                safe_merged_content = merged_chunk.get('content', '') if merged_chunk else ''

                                self.question_queue.put({
                                    'type': 'merge_info',
                                    'data': {
                                        'filename': safe_merged_filename,
                                        'original_index': i,
                                        'merged_to_index': next_index - 1,
                                        'char_count': safe_merged_char_count
                                    }
                                })

                                merged_quiz_data = processor.llm_client.generate_quiz(
                                    content=safe_merged_content,
                                    source_filename=safe_merged_filename,
                                    question_counts=processor.question_calculator.calculate_question_counts(safe_merged_char_count)
                                )

                                if merged_quiz_data and 'questions' in merged_quiz_data:
                                    chunk_questions = []
                                    for question in merged_quiz_data['questions']:
                                        if not isinstance(question, dict):
                                            logger.warning(f"process_with_progress_callback: 合并后检测到非字典格式题目或None值，跳过处理: {question}")
                                            continue
                                        question['source_file'] = safe_merged_filename
                                        question['chunk_index'] = merged_chunk.get('chunk_index', 0) if merged_chunk else 0
                                        question['merged_from'] = merged_chunk.get('merged_from', []) if merged_chunk else []
